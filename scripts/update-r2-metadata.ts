/**
 * Cloudflare R2 元数据更新脚本
 *
 * 该脚本用于批量更新 Cloudflare R2 存储桶中的文件元数据，
 * 添加 Content-Disposition: attachment 头，使浏览器下载文件而不是显示。
 *
 * 使用方法:
 * 1. 确保已安装 AWS CLI 并配置了 Cloudflare R2 凭证
 * 2. 运行: npx tsx scripts/update-r2-metadata.ts
 *
 * 注意: 该脚本使用 AWS CLI 与 Cloudflare R2 交互，因为 R2 兼容 S3 API
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件的目录路径（ES模块中的__dirname替代方案）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从 .env 文件加载环境变量
dotenv.config();

// 配置接口
interface Config {
  bucketName: string;
  endpointUrl: string;
  fileExtensions: string[];
  prefixes: string[];
  dryRun: boolean;
  batchSize: number;
  logFile: string;
  skipProcessed: boolean;
  processedCacheFile: string;
}

// S3 对象接口
interface S3Object {
  Key: string;
  LastModified?: string;
  ETag?: string;
  Size?: number;
  StorageClass?: string;
}

// 列表结果接口
interface ListObjectsResult {
  Contents?: S3Object[];
  NextToken?: string;
}

// 更新结果接口
interface UpdateResult {
  success: boolean;
  skipped: boolean;
}

// 配置
const CONFIG: Config = {
  // R2 存储桶名称
  bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'uslocal',

  // R2 S3 API 端点 URL
  endpointUrl: `https://${process.env.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,

  // 要处理的文件扩展名
  fileExtensions: ['pdf', 'png'],

  // 要处理的文件夹前缀 (留空处理整个存储桶)
  prefixes: ['coloring-pages/'],

  // 是否仅模拟执行 (设为 true 进行测试，不会实际修改文件；设为 false 将实际执行修改)
  dryRun: false,

  // 每次处理的最大文件数
  batchSize: 100,

  // 日志文件路径
  logFile: path.join(__dirname, 'r2-metadata-update.log'),

  // 是否跳过已处理的文件
  skipProcessed: true,

  // 已处理文件的缓存文件
  processedCacheFile: path.join(__dirname, 'r2-processed-files.json')
};

// 初始化日志
const log = (message: string): void => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(CONFIG.logFile, logMessage + '\n');
};

// 加载已处理文件的缓存
let processedFiles = new Set<string>();
if (CONFIG.skipProcessed && fs.existsSync(CONFIG.processedCacheFile)) {
  try {
    const processedData: string[] = JSON.parse(fs.readFileSync(CONFIG.processedCacheFile, 'utf-8'));
    processedFiles = new Set(processedData);
    log(`已加载 ${processedFiles.size} 个已处理文件的记录`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log(`加载已处理文件缓存时出错: ${errorMessage}`);
    processedFiles = new Set();
  }
}

// 保存已处理文件的缓存
const saveProcessedFiles = (): void => {
  if (CONFIG.skipProcessed) {
    try {
      fs.writeFileSync(CONFIG.processedCacheFile, JSON.stringify([...processedFiles]), 'utf-8');
      log(`已保存 ${processedFiles.size} 个已处理文件的记录`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      log(`保存已处理文件缓存时出错: ${errorMessage}`);
    }
  }
};

// 追加日志而不是清除
if (!fs.existsSync(CONFIG.logFile)) {
  fs.writeFileSync(CONFIG.logFile, '', 'utf-8');
}

log('开始更新 Cloudflare R2 文件元数据...');
log(`配置: ${JSON.stringify(CONFIG, null, 2)}`);

// 获取存储桶中的所有对象（使用分页）
const listObjects = async (prefix: string = ''): Promise<ListObjectsResult> => {
  try {
    let allObjects: S3Object[] = [];
    let nextToken: string | null = null;

    do {
      let command = `aws s3api list-objects-v2 --bucket ${CONFIG.bucketName} --prefix "${prefix}" --max-items 1000 --endpoint-url ${CONFIG.endpointUrl}`;

      if (nextToken) {
        command += ` --starting-token ${nextToken}`;
      }

      log(`执行命令: ${command}`);

      if (CONFIG.dryRun) {
        log('[DRY RUN] 跳过执行 list-objects-v2 命令');
        return { Contents: [] };
      }

      const output = execSync(command, { encoding: 'utf-8', maxBuffer: 10 * 1024 * 1024 }); // 增加缓冲区大小
      const result: ListObjectsResult = JSON.parse(output);

      if (result.Contents) {
        allObjects = allObjects.concat(result.Contents);
      }

      nextToken = result.NextToken || null;

      if (nextToken) {
        log(`获取到 ${allObjects.length} 个对象，继续获取下一页...`);
      }
    } while (nextToken);

    return { Contents: allObjects };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log(`列出对象时出错: ${errorMessage}`);
    return { Contents: [] };
  }
};

// 更新单个对象的元数据
const updateObjectMetadata = (key: string): UpdateResult => {
  try {
    // 检查是否已处理过该文件
    if (CONFIG.skipProcessed && processedFiles.has(key)) {
      log(`跳过已处理的文件: ${key}`);
      return { success: true, skipped: true };
    }

    // 获取文件扩展名
    const ext = path.extname(key).toLowerCase().substring(1);

    // 如果不是我们要处理的扩展名，则跳过
    if (!CONFIG.fileExtensions.includes(ext)) {
      log(`跳过非目标文件类型: ${key}`);
      return { success: false, skipped: true };
    }

    // 设置适当的 Content-Type
    let contentType = 'application/octet-stream';
    if (ext === 'pdf') {
      contentType = 'application/pdf';
    } else if (ext === 'png') {
      contentType = 'image/png';
    } else if (ext === 'jpg' || ext === 'jpeg') {
      contentType = 'image/jpeg';
    } else if (ext === 'webp') {
      contentType = 'image/webp';
    }

    // 从路径中提取文件名
    const fileName = key.split('/').pop() || key;

    // 构建 copy-object 命令
    const command = `aws s3api copy-object \\
      --bucket ${CONFIG.bucketName} \\
      --copy-source ${CONFIG.bucketName}/${key} \\
      --key ${key} \\
      --metadata-directive REPLACE \\
      --content-type "${contentType}" \\
      --content-disposition "attachment; filename=\\"${fileName}\\"" \\
      --endpoint-url ${CONFIG.endpointUrl}`;

    log(`执行命令: ${command}`);

    if (CONFIG.dryRun) {
      log(`[DRY RUN] 跳过执行 copy-object 命令: ${key}`);
      return { success: true, skipped: false };
    }

    execSync(command, { encoding: 'utf-8' });
    log(`成功更新对象元数据: ${key}`);

    // 添加到已处理文件集合
    if (CONFIG.skipProcessed) {
      processedFiles.add(key);
    }

    return { success: true, skipped: false };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log(`更新对象元数据时出错 (${key}): ${errorMessage}`);
    return { success: false, skipped: false };
  }
};

// 主函数
const main = async (): Promise<void> => {
  let totalProcessed = 0;
  let totalSuccess = 0;
  let totalSkipped = 0;
  let totalFailed = 0;
  let totalAlreadyProcessed = 0;

  // 如果没有指定前缀，则处理整个存储桶
  const prefixesToProcess = CONFIG.prefixes.length > 0 ? CONFIG.prefixes : [''];

  try {
    for (const prefix of prefixesToProcess) {
      log(`处理前缀: ${prefix || '(根目录)'}`);

      const result = await listObjects(prefix);
      const objects = result.Contents || [];

      log(`找到 ${objects.length} 个对象`);

      // 按批次处理对象
      for (let i = 0; i < objects.length; i += CONFIG.batchSize) {
        const batch = objects.slice(i, i + CONFIG.batchSize);
        log(`处理批次 ${Math.floor(i / CONFIG.batchSize) + 1}/${Math.ceil(objects.length / CONFIG.batchSize)} (${batch.length} 个对象)`);

        for (const object of batch) {
          totalProcessed++;
          const key = object.Key;

          const result = updateObjectMetadata(key);

          if (result.success) {
            if (result.skipped) {
              totalAlreadyProcessed++;
            } else {
              totalSuccess++;
            }
          } else {
            if (result.skipped) {
              totalSkipped++;
            } else {
              totalFailed++;
            }
          }

          // 每处理100个文件保存一次缓存
          if (CONFIG.skipProcessed && totalProcessed % 100 === 0) {
            saveProcessedFiles();
          }
        }
      }
    }

    // 最后保存一次缓存
    if (CONFIG.skipProcessed) {
      saveProcessedFiles();
    }

    log('完成更新 Cloudflare R2 文件元数据');
    log(`总结: 处理 ${totalProcessed} 个对象, 成功 ${totalSuccess}, 已处理跳过 ${totalAlreadyProcessed}, 类型跳过 ${totalSkipped}, 失败 ${totalFailed}`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log(`执行过程中出错: ${errorMessage}`);

    // 出错时也保存缓存
    if (CONFIG.skipProcessed) {
      saveProcessedFiles();
    }
  }
};

// 执行主函数
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  log(`脚本执行出错: ${errorMessage}`);

  // 出错时也保存缓存
  if (CONFIG.skipProcessed) {
    saveProcessedFiles();
  }

  process.exit(1);
});
