/**
 * Cloudflare R2 批量上传脚本
 *
 * 该脚本用于批量将本地文件夹中的文件上传到 Cloudflare R2 存储桶，
 * 并设置正确的 Content-Type 和 Content-Disposition: attachment 头。
 *
 * 使用方法:
 * 1. 确保已安装 AWS CLI 并配置了 Cloudflare R2 凭证
 * 2. 运行: npx tsx scripts/batch-upload-to-r2.ts <本地文件夹路径> <R2目标前缀>
 *
 * 示例:
 * npx tsx scripts/batch-upload-to-r2.ts ./public/files coloring-pages
 *
 * 注意: 该脚本使用 AWS CLI 与 Cloudflare R2 交互，因为 R2 兼容 S3 API
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件的目录路径（ES模块中的__dirname替代方案）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从 .env 文件加载环境变量
dotenv.config();

// 配置接口
interface Config {
  bucketName: string;
  endpointUrl: string;
  fileExtensions: string[];
  dryRun: boolean;
  batchSize: number;
  logFile: string;
}

// 配置
const CONFIG: Config = {
  // R2 存储桶名称
  bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'uslocal',

  // R2 S3 API 端点 URL
  endpointUrl: `https://${process.env.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,

  // 要处理的文件扩展名
  fileExtensions: ['pdf', 'png', 'jpg', 'jpeg', 'webp', 'svg'],

  // 是否仅模拟执行 (设为 true 进行测试，不会实际上传文件；设为 false 将实际执行上传)
  dryRun: false,

  // 每次处理的最大文件数
  batchSize: 10,

  // 日志文件路径
  logFile: path.join(__dirname, 'r2-batch-upload.log')
};

// 初始化日志
const log = (message: string): void => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(CONFIG.logFile, logMessage + '\n');
};

// 清除旧日志
if (fs.existsSync(CONFIG.logFile)) {
  fs.unlinkSync(CONFIG.logFile);
}

// 检查命令行参数
if (process.argv.length < 4) {
  log('错误: 缺少必要的参数');
  log('用法: npx ts-node batch-upload-to-r2.ts <本地文件夹路径> <R2目标前缀>');
  process.exit(1);
}

const localFolderPath: string = process.argv[2];
const r2DestinationPrefix: string = process.argv[3];

// 检查本地文件夹是否存在
if (!fs.existsSync(localFolderPath) || !fs.statSync(localFolderPath).isDirectory()) {
  log(`错误: 本地文件夹不存在或不是文件夹: ${localFolderPath}`);
  process.exit(1);
}

log('开始批量上传文件到 Cloudflare R2...');
log(`本地文件夹: ${localFolderPath}`);
log(`R2 目标前缀: ${r2DestinationPrefix}`);

// 递归获取文件夹中的所有文件
const getAllFiles = (dir: string, fileList: string[] = []): string[] => {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList);
    } else {
      const ext = path.extname(file).toLowerCase().substring(1);
      if (CONFIG.fileExtensions.includes(ext)) {
        fileList.push(filePath);
      }
    }
  });

  return fileList;
};

// 上传单个文件并设置元数据
const uploadFile = (localFilePath: string, r2Key: string): boolean => {
  try {
    // 获取文件扩展名
    const ext = path.extname(localFilePath).toLowerCase().substring(1);

    // 设置适当的 Content-Type
    let contentType = 'application/octet-stream';
    if (ext === 'pdf') {
      contentType = 'application/pdf';
    } else if (ext === 'png') {
      contentType = 'image/png';
    } else if (ext === 'jpg' || ext === 'jpeg') {
      contentType = 'image/jpeg';
    } else if (ext === 'webp') {
      contentType = 'image/webp';
    } else if (ext === 'svg') {
      contentType = 'image/svg+xml';
    }

    // 从路径中提取文件名
    const fileName = path.basename(localFilePath);

    // 构建 put-object 命令
    const command = `aws s3api put-object \\
      --bucket ${CONFIG.bucketName} \\
      --key ${r2Key} \\
      --body "${localFilePath}" \\
      --content-type "${contentType}" \\
      --content-disposition "attachment; filename=\\"${fileName}\\"" \\
      --endpoint-url ${CONFIG.endpointUrl}`;

    log(`执行命令: ${command}`);

    if (CONFIG.dryRun) {
      log(`[DRY RUN] 跳过执行 put-object 命令: ${r2Key}`);
      return true;
    }

    execSync(command, { encoding: 'utf-8' });
    log(`成功上传文件: ${r2Key}`);
    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log(`上传文件时出错 (${r2Key}): ${errorMessage}`);
    return false;
  }
};

// 主函数
const main = async (): Promise<void> => {
  // 获取所有文件
  const allFiles = getAllFiles(localFolderPath);
  log(`找到 ${allFiles.length} 个文件需要上传`);

  let totalProcessed = 0;
  let totalSuccess = 0;
  let totalFailed = 0;

  // 按批次处理文件
  for (let i = 0; i < allFiles.length; i += CONFIG.batchSize) {
    const batch = allFiles.slice(i, i + CONFIG.batchSize);
    log(`处理批次 ${Math.floor(i / CONFIG.batchSize) + 1}/${Math.ceil(allFiles.length / CONFIG.batchSize)} (${batch.length} 个文件)`);

    for (const localFilePath of batch) {
      totalProcessed++;

      // 计算 R2 目标路径
      const relativePath = path.relative(localFolderPath, localFilePath);
      const r2Key = path.join(r2DestinationPrefix, relativePath).replace(/\\/g, '/');

      const success = uploadFile(localFilePath, r2Key);
      if (success) {
        totalSuccess++;
      } else {
        totalFailed++;
      }
    }
  }

  log('完成批量上传文件到 Cloudflare R2');
  log(`总结: 处理 ${totalProcessed} 个文件, 成功 ${totalSuccess}, 失败 ${totalFailed}`);
};

// 执行主函数
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  log(`脚本执行出错: ${errorMessage}`);
  process.exit(1);
});
