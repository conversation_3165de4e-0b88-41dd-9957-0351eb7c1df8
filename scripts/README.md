# Scripts Documentation

This directory contains TypeScript scripts for managing Cloudflare R2 storage and content synchronization.

## Prerequisites

1. **Environment Variables**: Ensure your `.env` file contains the following Cloudflare R2 credentials:
   ```
   CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key_id
   CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_access_key
   CLOUDFLARE_R2_ACCOUNT_ID=your_account_id
   CLOUDFLARE_R2_BUCKET_NAME=your_bucket_name
   CLOUDFLARE_R2_PUBLIC_URL=https://static.printablecoloringhub.com
   ```

2. **AWS CLI**: Install and configure AWS CLI to work with Cloudflare R2:
   ```bash
   npx tsx scripts/configure-aws-cli.ts
   ```

## Available Scripts

### 1. configure-aws-cli.ts
Configures AWS CLI to use Cloudflare R2 credentials from your `.env` file.

**Usage:**
```bash
npx tsx scripts/configure-aws-cli.ts
```

### 2. upload-to-r2-with-metadata.ts
Uploads a single file to Cloudflare R2 with proper metadata and Content-Disposition headers.

**Usage:**
```bash
npx tsx scripts/upload-to-r2-with-metadata.ts <local-file-path> <r2-destination-path>
```

**Example:**
```bash
npx tsx scripts/upload-to-r2-with-metadata.ts ./files/sample.pdf coloring-pages/sample.pdf
```

### 3. batch-upload-to-r2.ts
Batch uploads files from a local directory to Cloudflare R2 with proper metadata.

**Usage:**
```bash
npx tsx scripts/batch-upload-to-r2.ts <local-folder-path> <r2-destination-prefix>
```

**Example:**
```bash
npx tsx scripts/batch-upload-to-r2.ts ./public/files coloring-pages
```

### 4. sync-singlefile-to-r2.ts
Syncs files from `scripts/resource/singlefile` directory to R2 storage with automatic folder structure.

**Usage:**
```bash
npx tsx scripts/sync-singlefile-to-r2.ts
```

### 5. update-r2-metadata.ts
Updates metadata for existing files in R2 storage, adding Content-Disposition: attachment headers.

**Usage:**
```bash
npx tsx scripts/update-r2-metadata.ts
```

### 6. sync-resource-to-content.ts
Comprehensive script that:
- Validates MDX files in `scripts/resource/coloring-pages`
- Moves valid MDX files to `src/content/coloring-pages`
- Uploads associated resource files to R2 storage
- Maintains processing logs and skips already processed files

**Usage:**
```bash
npx tsx scripts/sync-resource-to-content.ts
```

### 7. check-r2-files.ts
Tests accessibility of files in R2 storage and generates a report.

**Usage:**
```bash
npx tsx scripts/check-r2-files.ts
```

## Configuration Options

Most scripts support the following configuration options (modify the `CONFIG` object in each script):

- **dryRun**: Set to `true` for testing without making actual changes
- **skipProcessed**: Skip files that have already been processed (uses local cache files)
- **batchSize**: Number of files to process in each batch
- **logFile**: Path to the log file for each script

## File Naming Conventions

The scripts expect the following file naming conventions:

- **Monochrome PNG**: `{assetFolder}_monochrome.png` or `{assetFolder}_m.png`
- **Monochrome PDF**: `{assetFolder}_monochrome.pdf` or `{assetFolder}_m.pdf`
- **Colored PNG**: `{assetFolder}_colored.png` or `{assetFolder}_c.png`

## Log Files

Each script generates its own log file in the `scripts` directory:

- `r2-upload.log` - Single file uploads
- `r2-batch-upload.log` - Batch uploads
- `singlefile-sync.log` - Single file sync
- `r2-metadata-update.log` - Metadata updates
- `coloring-page-processor.log` - Resource to content sync
- `r2-files-check-results.json` - File accessibility check results

## Cache Files

Some scripts maintain cache files to track processed items:

- `r2-processed-files.json` - Tracks files processed by metadata update script
- `upload-record.json` - Tracks resources processed by sync script

These files are automatically managed and should be added to `.gitignore`.

## Error Handling

All scripts include comprehensive error handling and logging. Check the respective log files for detailed information about any failures.

## TypeScript Benefits

The conversion to TypeScript provides:

- **Type Safety**: Compile-time type checking prevents runtime errors
- **Better IDE Support**: Enhanced autocomplete and error detection
- **Interface Definitions**: Clear contracts for data structures
- **Improved Maintainability**: Self-documenting code with type annotations

## Running Scripts

All scripts should be run from the project root directory using `npx tsx` to ensure proper TypeScript compilation and execution.

## Migration from JavaScript

All scripts have been migrated from JavaScript to TypeScript. The old `.js` files have been removed. Use `npx tsx` instead of `node` to run the scripts.
