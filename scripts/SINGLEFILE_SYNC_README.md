# 单文件同步脚本使用说明

## 概述

`sync-singlefile-to-r2.js` 脚本用于将 `scripts/resource/singlefile` 目录下的文件同步到 Cloudflare R2 存储桶的 `coloring-pages` 目录。

## 功能特性

- ✅ 自动上传支持的文件类型（PNG、PDF、WebP、JPG、JPEG）
- ✅ 覆盖已存在的文件
- ✅ 自动设置 `Content-Disposition: attachment` 头部
- ✅ 智能文件路径解析
- ✅ 详细的日志记录
- ✅ 支持 Dry Run 模式

## 文件命名规则

脚本会根据文件名自动解析目标路径：

### 标准命名格式
```
assetFolder_monochrome.png  -> coloring-pages/assetFolder/assetFolder_monochrome.png
assetFolder_colored.webp    -> coloring-pages/assetFolder/assetFolder_colored.webp
assetFolder_m.pdf           -> coloring-pages/assetFolder/assetFolder_m.pdf
assetFolder_c.png           -> coloring-pages/assetFolder/assetFolder_c.png
```

### 自定义命名
```
my-custom-file.png          -> coloring-pages/my-custom-file/my-custom-file.png
```

## 使用方法

### 1. 准备文件
将需要上传的文件放入 `scripts/resource/singlefile` 目录：

```bash
mkdir -p scripts/resource/singlefile
cp your-files/* scripts/resource/singlefile/
```

### 2. 测试运行（推荐）
首先使用 Dry Run 模式测试：

```bash
# 编辑脚本，设置 dryRun: true
node scripts/sync-singlefile-to-r2.js
```

### 3. 正式运行
确认无误后，设置 `dryRun: false` 并执行：

```bash
node scripts/sync-singlefile-to-r2.js
```

## 配置选项

在脚本中可以修改以下配置：

```javascript
const CONFIG = {
  // 单文件资源目录
  singleFileDir: path.join(__dirname, 'resource/singlefile'),
  
  // R2 存储桶名称
  bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'uslocal',
  
  // R2 基础路径
  r2BasePath: 'coloring-pages',
  
  // 是否仅模拟执行
  dryRun: false,
  
  // 支持的文件类型
  supportedExtensions: ['.png', '.pdf', '.webp', '.jpg', '.jpeg']
};
```

## 环境变量

确保 `.env` 文件包含以下变量：

```env
CLOUDFLARE_R2_BUCKET_NAME=your-bucket-name
CLOUDFLARE_R2_ACCOUNT_ID=your-account-id
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
```

## 日志文件

脚本会生成详细的日志文件：`scripts/singlefile-sync.log`

## 示例输出

```
[2024-01-01T12:00:00.000Z] 开始单文件同步任务...
[2024-01-01T12:00:00.001Z] 找到 3 个文件需要同步
[2024-01-01T12:00:00.002Z] 处理文件: hello-kitty_monochrome.png
[2024-01-01T12:00:00.003Z] 成功上传文件: coloring-pages/hello-kitty/hello-kitty_monochrome.png
[2024-01-01T12:00:00.004Z] 处理文件: hello-kitty_colored.webp
[2024-01-01T12:00:00.005Z] 成功上传文件: coloring-pages/hello-kitty/hello-kitty_colored.webp
[2024-01-01T12:00:00.006Z] 单文件同步完成
[2024-01-01T12:00:00.007Z] 总结: 总计 3 个文件, 成功 3, 跳过 0, 失败 0
```

## 注意事项

1. **文件覆盖**：脚本会覆盖 R2 中已存在的同名文件
2. **网络连接**：确保网络连接稳定，大文件上传可能需要较长时间
3. **权限检查**：确保 AWS 凭证有足够的权限操作 R2 存储桶
4. **文件大小**：注意 R2 的文件大小限制

## 故障排除

### 常见错误

1. **目录不存在**
   ```
   错误: 单文件目录不存在
   ```
   解决：创建 `scripts/resource/singlefile` 目录

2. **AWS 凭证错误**
   ```
   上传文件时出错: The AWS Access Key Id you provided does not exist
   ```
   解决：检查 `.env` 文件中的 AWS 凭证

3. **网络超时**
   ```
   上传文件时出错: timeout
   ```
   解决：检查网络连接，重试上传

### 调试模式

启用详细日志：
```bash
DEBUG=* node scripts/sync-singlefile-to-r2.js
```
