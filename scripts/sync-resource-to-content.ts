/**
 * 资源同步脚本
 *
 * 该脚本用于同步 scripts/resource 目录下的资源文件：
 * 1. 检查 MDX 文件是否合格
 * 2. 将合格的 MDX 文件移动到 src/content/coloring-pages 目录
 * 3. 将其他资源文件（PNG、PDF）上传到 R2 存储桶
 * 4. 记录处理日志，默认跳过已完成的文件
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import matter from 'gray-matter';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从 .env 文件加载环境变量
dotenv.config();

// 配置接口
interface Config {
  resourceDir: string;
  contentDir: string;
  bucketName: string;
  endpointUrl: string;
  r2BasePath: string;
  logFile: string;
  processedRecordFile: string;
  skipProcessed: boolean;
  dryRun: boolean;
  requiredMdxFields: string[];
  requiredAssetFiles: string[];
}

// 验证结果接口
interface ValidationResult {
  valid: boolean;
  message?: string;
  missingFiles?: string[];
}

// 上传结果接口
interface UploadResult {
  success: boolean;
  skipped: boolean;
}

// 上传统计接口
interface UploadStats {
  total: number;
  success: number;
  skipped: number;
  failed: number;
}

// 处理结果接口
interface ProcessResult {
  success: boolean;
  skipped?: boolean;
  message?: string;
  uploadResults?: UploadStats;
}

// 总体结果接口
interface OverallResults {
  total: number;
  success: number;
  skipped: number;
  failed: number;
}

// 配置
const CONFIG: Config = {
  // 资源目录
  resourceDir: path.join(__dirname, 'resource/coloring-pages'),

  // 目标内容目录
  contentDir: path.join(__dirname, '../src/content/coloring-pages'),

  // R2 存储桶名称
  bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'uslocal',

  // R2 S3 API 端点 URL
  endpointUrl: `https://${process.env.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,

  // R2 基础路径
  r2BasePath: 'coloring-pages',

  // 日志文件路径
  logFile: path.join(__dirname, 'coloring-page-processor.log'),

  // 处理记录文件
  processedRecordFile: path.join(__dirname, 'upload-record.json'),

  // 是否跳过已处理的文件
  skipProcessed: true,

  // 是否仅模拟执行 (设为 true 进行测试，不会实际修改文件；设为 false 将实际执行修改)
  dryRun: false,

  // 必需的 MDX 前置元数据字段
  requiredMdxFields: [
    'title', 'id', 'assetFolder', 'description', 'categoryInfo',
    'tags', 'popular', 'featured', 'premium', 'dateAdded'
  ],

  // 必需的资源文件
  requiredAssetFiles: [
    '_monochrome.png',
    '_monochrome.pdf'
  ]
};

// 初始化日志
const log = (message: string): void => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(CONFIG.logFile, logMessage + '\n');
};

// 追加日志而不是清除
if (!fs.existsSync(CONFIG.logFile)) {
  fs.writeFileSync(CONFIG.logFile, '', 'utf-8');
}

// 加载已处理记录
let processedRecords: Record<string, string[]> = {};
if (CONFIG.skipProcessed && fs.existsSync(CONFIG.processedRecordFile)) {
  try {
    processedRecords = JSON.parse(fs.readFileSync(CONFIG.processedRecordFile, 'utf-8'));
    log(`已加载 ${Object.keys(processedRecords).length} 个已处理资源的记录`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log(`加载已处理记录时出错: ${errorMessage}`);
    processedRecords = {};
  }
}

// 保存处理记录
const saveProcessedRecords = (): void => {
  if (CONFIG.skipProcessed) {
    try {
      fs.writeFileSync(CONFIG.processedRecordFile, JSON.stringify(processedRecords, null, 2), 'utf-8');
      log(`已保存 ${Object.keys(processedRecords).length} 个已处理资源的记录`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      log(`保存已处理记录时出错: ${errorMessage}`);
    }
  }
};

// 检查 MDX 文件是否合格
const validateMdxFile = (filePath: string): ValidationResult => {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const { data: frontMatter, content } = matter(fileContent);

    // 检查必需字段
    const missingFields = CONFIG.requiredMdxFields.filter(field => {
      if (field === 'categoryInfo') {
        return !frontMatter.categoryInfo ||
               !frontMatter.categoryInfo.main ||
               !frontMatter.categoryInfo.sub;
      }
      return frontMatter[field] === undefined;
    });

    if (missingFields.length > 0) {
      return {
        valid: false,
        message: `缺少必需的前置元数据字段: ${missingFields.join(', ')}`
      };
    }

    // 检查内容长度
    if (content.trim().length < 100) {
      return {
        valid: false,
        message: '内容太短，需要更详细的描述'
      };
    }

    return { valid: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      valid: false,
      message: `验证 MDX 文件时出错: ${errorMessage}`
    };
  }
};

// 检查资源文件是否完整
const validateAssetFiles = (dirPath: string, assetFolder: string): ValidationResult => {
  const missingFiles: string[] = [];

  for (const requiredFile of CONFIG.requiredAssetFiles) {
    const filePath = path.join(dirPath, `${assetFolder}${requiredFile}`);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(`${assetFolder}${requiredFile}`);
    }
  }

  return {
    valid: missingFiles.length === 0,
    missingFiles
  };
};

// 上传文件到 R2 存储桶
const uploadFileToR2 = (localFilePath: string, assetFolder: string, fileName: string): UploadResult => {
  try {
    // 检查是否已处理过该文件
    const r2Key = `${CONFIG.r2BasePath}/${assetFolder}/${fileName}`;
    if (CONFIG.skipProcessed &&
        processedRecords[assetFolder] &&
        processedRecords[assetFolder].includes(fileName)) {
      log(`跳过已处理的文件: ${r2Key}`);
      return { success: true, skipped: true };
    }

    // 获取文件扩展名
    const ext = path.extname(fileName).toLowerCase().substring(1);

    // 设置适当的 Content-Type
    let contentType = 'application/octet-stream';
    if (ext === 'pdf') {
      contentType = 'application/pdf';
    } else if (ext === 'png') {
      contentType = 'image/png';
    } else if (ext === 'jpg' || ext === 'jpeg') {
      contentType = 'image/jpeg';
    } else if (ext === 'webp') {
      contentType = 'image/webp';
    }

    // 构建 put-object 命令
    const command = `aws s3api put-object \\
      --bucket ${CONFIG.bucketName} \\
      --key ${r2Key} \\
      --body "${localFilePath}" \\
      --content-type "${contentType}" \\
      --content-disposition "attachment; filename=\\"${fileName}\\"" \\
      --endpoint-url ${CONFIG.endpointUrl}`;

    log(`执行命令: ${command}`);

    if (CONFIG.dryRun) {
      log(`[DRY RUN] 跳过执行 put-object 命令: ${r2Key}`);
      return { success: true, skipped: false };
    }

    execSync(command, { encoding: 'utf-8' });
    log(`成功上传文件: ${r2Key}`);

    // 记录已处理的文件
    if (!processedRecords[assetFolder]) {
      processedRecords[assetFolder] = [];
    }
    if (!processedRecords[assetFolder].includes(fileName)) {
      processedRecords[assetFolder].push(fileName);
    }

    return { success: true, skipped: false };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log(`上传文件时出错 (${assetFolder}/${fileName}): ${errorMessage}`);
    return { success: false, skipped: false };
  }
};

// 处理单个资源目录
const processResourceDir = (dirName: string): ProcessResult => {
  const resourceDirPath = path.join(CONFIG.resourceDir, dirName);

  // 检查是否是目录
  if (!fs.statSync(resourceDirPath).isDirectory()) {
    return {
      success: false,
      message: `${dirName} 不是目录`
    };
  }

  log(`处理资源目录: ${dirName}`);

  // 检查是否已完全处理过
  if (CONFIG.skipProcessed &&
      processedRecords[dirName] &&
      processedRecords[dirName].includes('__COMPLETED__')) {
    log(`跳过已完全处理的资源目录: ${dirName}`);
    return { success: true, skipped: true };
  }

  // 查找 MDX 文件
  const files = fs.readdirSync(resourceDirPath);
  const mdxFile = files.find(file => file.endsWith('.mdx'));

  if (!mdxFile) {
    return {
      success: false,
      message: `${dirName} 目录中没有找到 MDX 文件`
    };
  }

  const mdxFilePath = path.join(resourceDirPath, mdxFile);

  // 验证 MDX 文件
  const mdxValidation = validateMdxFile(mdxFilePath);
  if (!mdxValidation.valid) {
    return {
      success: false,
      message: `MDX 文件验证失败: ${mdxValidation.message}`
    };
  }

  // 验证资源文件
  const assetValidation = validateAssetFiles(resourceDirPath, dirName);
  if (!assetValidation.valid) {
    return {
      success: false,
      message: `资源文件不完整，缺少: ${assetValidation.missingFiles?.join(', ')}`
    };
  }

  // 移动 MDX 文件到内容目录
  const targetMdxPath = path.join(CONFIG.contentDir, mdxFile);

  if (CONFIG.dryRun) {
    log(`[DRY RUN] 跳过移动 MDX 文件: ${mdxFilePath} -> ${targetMdxPath}`);
  } else {
    try {
      fs.copyFileSync(mdxFilePath, targetMdxPath);
      log(`成功移动 MDX 文件: ${mdxFilePath} -> ${targetMdxPath}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        message: `移动 MDX 文件时出错: ${errorMessage}`
      };
    }
  }

  // 上传资源文件到 R2
  const assetFiles = files.filter(file =>
    file !== mdxFile &&
    (file.endsWith('.png') || file.endsWith('.pdf') || file.endsWith('.webp'))
  );

  let uploadResults: UploadStats = {
    total: assetFiles.length,
    success: 0,
    skipped: 0,
    failed: 0
  };

  for (const assetFile of assetFiles) {
    const assetFilePath = path.join(resourceDirPath, assetFile);
    const result = uploadFileToR2(assetFilePath, dirName, assetFile);

    if (result.success) {
      if (result.skipped) {
        uploadResults.skipped++;
      } else {
        uploadResults.success++;
      }
    } else {
      uploadResults.failed++;
    }
  }

  // 如果所有操作都成功，标记为完全处理
  if (uploadResults.failed === 0 && !CONFIG.dryRun) {
    if (!processedRecords[dirName]) {
      processedRecords[dirName] = [];
    }
    if (!processedRecords[dirName].includes('__COMPLETED__')) {
      processedRecords[dirName].push('__COMPLETED__');
    }
  }

  return {
    success: uploadResults.failed === 0,
    uploadResults
  };
};

// 主函数
const main = async (): Promise<void> => {
  log('开始同步资源文件...');
  log(`配置: ${JSON.stringify({
    resourceDir: CONFIG.resourceDir,
    contentDir: CONFIG.contentDir,
    skipProcessed: CONFIG.skipProcessed,
    dryRun: CONFIG.dryRun
  }, null, 2)}`);

  // 确保目录存在
  if (!fs.existsSync(CONFIG.resourceDir)) {
    log(`错误: 资源目录不存在: ${CONFIG.resourceDir}`);
    process.exit(1);
  }

  if (!fs.existsSync(CONFIG.contentDir)) {
    log(`错误: 内容目录不存在: ${CONFIG.contentDir}`);
    process.exit(1);
  }

  // 获取所有资源目录
  const resourceDirs = fs.readdirSync(CONFIG.resourceDir)
    .filter(item => {
      const itemPath = path.join(CONFIG.resourceDir, item);
      return fs.statSync(itemPath).isDirectory();
    });

  log(`找到 ${resourceDirs.length} 个资源目录`);

  let results: OverallResults = {
    total: resourceDirs.length,
    success: 0,
    skipped: 0,
    failed: 0
  };

  // 处理每个资源目录
  for (const dirName of resourceDirs) {
    const result = processResourceDir(dirName);

    if (result.success) {
      if (result.skipped) {
        results.skipped++;
        log(`跳过已处理的资源目录: ${dirName}`);
      } else {
        results.success++;
        log(`成功处理资源目录: ${dirName}`);
        if (result.uploadResults) {
          log(`上传结果: 总计 ${result.uploadResults.total}, 成功 ${result.uploadResults.success}, 跳过 ${result.uploadResults.skipped}, 失败 ${result.uploadResults.failed}`);
        }
      }
    } else {
      results.failed++;
      log(`处理资源目录失败: ${dirName}, 原因: ${result.message}`);
    }

    // 每处理 5 个目录保存一次记录
    if ((results.success + results.skipped + results.failed) % 5 === 0) {
      saveProcessedRecords();
    }
  }

  // 保存最终处理记录
  saveProcessedRecords();

  log('资源同步完成');
  log(`总结: 总计 ${results.total} 个资源目录, 成功 ${results.success}, 跳过 ${results.skipped}, 失败 ${results.failed}`);
};

// 执行主函数
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  log(`脚本执行出错: ${errorMessage}`);
  saveProcessedRecords();
  process.exit(1);
});
