[2025-05-24T01:19:47.187Z] 开始单文件同步任务...
[2025-05-24T01:19:47.192Z] 配置: {
  "singleFileDir": "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile",
  "bucketName": "uslocal",
  "r2BasePath": "coloring-pages",
  "dryRun": false
}
[2025-05-24T01:19:47.192Z] 开始同步单文件目录...
[2025-05-24T01:19:47.194Z] 找到 9 个文件需要同步
[2025-05-24T01:19:47.194Z] 处理文件: alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf
[2025-05-24T01:19:47.194Z] 准备上传文件: alien-spaceship-adventure_monochrome.pdf -> coloring-pages/alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf
[2025-05-24T01:19:47.194Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"alien-spaceship-adventure_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:19:49.398Z] 成功上传文件: coloring-pages/alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf
[2025-05-24T01:19:49.398Z] 成功上传文件: alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf -> coloring-pages/alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf
[2025-05-24T01:19:49.399Z] 处理文件: buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf
[2025-05-24T01:19:49.399Z] 准备上传文件: buzz-lightyear-space-ranger_monochrome.pdf -> coloring-pages/buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf
[2025-05-24T01:19:49.399Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"buzz-lightyear-space-ranger_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:19:51.399Z] 成功上传文件: coloring-pages/buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf
[2025-05-24T01:19:51.399Z] 成功上传文件: buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf -> coloring-pages/buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf
[2025-05-24T01:19:51.399Z] 处理文件: cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf
[2025-05-24T01:19:51.399Z] 准备上传文件: cosmic-rocket-adventure_monochrome.pdf -> coloring-pages/cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf
[2025-05-24T01:19:51.400Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"cosmic-rocket-adventure_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:19:53.310Z] 成功上传文件: coloring-pages/cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf
[2025-05-24T01:19:53.310Z] 成功上传文件: cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf -> coloring-pages/cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf
[2025-05-24T01:19:53.311Z] 处理文件: cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf
[2025-05-24T01:19:53.311Z] 准备上传文件: cowboy-and-cowgirl-pals_monochrome.pdf -> coloring-pages/cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf
[2025-05-24T01:19:53.311Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"cowboy-and-cowgirl-pals_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:19:55.471Z] 成功上传文件: coloring-pages/cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf
[2025-05-24T01:19:55.471Z] 成功上传文件: cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf -> coloring-pages/cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf
[2025-05-24T01:19:55.471Z] 处理文件: majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf
[2025-05-24T01:19:55.472Z] 准备上传文件: majestic-medieval-castle_monochrome.pdf -> coloring-pages/majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf
[2025-05-24T01:19:55.472Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"majestic-medieval-castle_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:19:57.477Z] 成功上传文件: coloring-pages/majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf
[2025-05-24T01:19:57.477Z] 成功上传文件: majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf -> coloring-pages/majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf
[2025-05-24T01:19:57.478Z] 处理文件: majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf
[2025-05-24T01:19:57.478Z] 准备上传文件: majestic-seaside-palace_monochrome.pdf -> coloring-pages/majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf
[2025-05-24T01:19:57.478Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"majestic-seaside-palace_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:19:59.747Z] 成功上传文件: coloring-pages/majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf
[2025-05-24T01:19:59.747Z] 成功上传文件: majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf -> coloring-pages/majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf
[2025-05-24T01:19:59.748Z] 处理文件: sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf
[2025-05-24T01:19:59.748Z] 准备上传文件: sheriff-woody-pride_monochrome.pdf -> coloring-pages/sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf
[2025-05-24T01:19:59.748Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"sheriff-woody-pride_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:20:01.717Z] 成功上传文件: coloring-pages/sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf
[2025-05-24T01:20:01.717Z] 成功上传文件: sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf -> coloring-pages/sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf
[2025-05-24T01:20:01.717Z] 处理文件: solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf
[2025-05-24T01:20:01.717Z] 准备上传文件: solar-system-planets-coloring_monochrome.pdf -> coloring-pages/solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf
[2025-05-24T01:20:01.717Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"solar-system-planets-coloring_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:20:03.734Z] 成功上传文件: coloring-pages/solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf
[2025-05-24T01:20:03.734Z] 成功上传文件: solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf -> coloring-pages/solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf
[2025-05-24T01:20:03.734Z] 处理文件: yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf
[2025-05-24T01:20:03.735Z] 准备上传文件: yodeling-cowgirl-jessie_monochrome.pdf -> coloring-pages/yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf
[2025-05-24T01:20:03.735Z] 执行命令: aws s3api put-object       --bucket uslocal       --key "coloring-pages/yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf"       --body "/Users/<USER>/printablecoloringhub/scripts/resource/singlefile/yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf"       --content-type "application/pdf"       --content-disposition "attachment; filename=\"yodeling-cowgirl-jessie_monochrome.pdf\""       --endpoint-url https://437421100b0bbe9a3a46bec1aa2d4418.r2.cloudflarestorage.com
[2025-05-24T01:20:05.796Z] 成功上传文件: coloring-pages/yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf
[2025-05-24T01:20:05.796Z] 成功上传文件: yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf -> coloring-pages/yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf
[2025-05-24T01:20:05.797Z] 单文件同步完成
[2025-05-24T01:20:05.797Z] 总结: 总计 9 个文件, 成功 9, 跳过 0, 失败 0
[2025-05-24T01:20:05.797Z] 成功上传的文件:
[2025-05-24T01:20:05.797Z]   - alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf -> coloring-pages/alien-spaceship-adventure/alien-spaceship-adventure_monochrome.pdf
[2025-05-24T01:20:05.797Z]   - buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf -> coloring-pages/buzz-lightyear-space-ranger/buzz-lightyear-space-ranger_monochrome.pdf
[2025-05-24T01:20:05.797Z]   - cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf -> coloring-pages/cosmic-rocket-adventure/cosmic-rocket-adventure_monochrome.pdf
[2025-05-24T01:20:05.797Z]   - cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf -> coloring-pages/cowboy-and-cowgirl-pals/cowboy-and-cowgirl-pals_monochrome.pdf
[2025-05-24T01:20:05.797Z]   - majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf -> coloring-pages/majestic-medieval-castle/majestic-medieval-castle_monochrome.pdf
[2025-05-24T01:20:05.797Z]   - majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf -> coloring-pages/majestic-seaside-palace/majestic-seaside-palace_monochrome.pdf
[2025-05-24T01:20:05.797Z]   - sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf -> coloring-pages/sheriff-woody-pride/sheriff-woody-pride_monochrome.pdf
[2025-05-24T01:20:05.797Z]   - solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf -> coloring-pages/solar-system-planets-coloring/solar-system-planets-coloring_monochrome.pdf
[2025-05-24T01:20:05.797Z]   - yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf -> coloring-pages/yodeling-cowgirl-jessie/yodeling-cowgirl-jessie_monochrome.pdf
