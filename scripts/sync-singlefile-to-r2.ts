/**
 * 单文件同步脚本
 *
 * 该脚本用于同步 scripts/resource/singlefile 目录下的文件到 R2 存储桶：
 * 1. 扫描 singlefile 目录下的所有文件
 * 2. 将文件上传到 R2 存储桶的 coloring-pages 目录
 * 3. 存在的文件会被覆盖
 * 4. 自动设置 Content-Disposition: attachment 头部
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从 .env 文件加载环境变量
dotenv.config();

// 配置接口
interface Config {
  singleFileDir: string;
  bucketName: string;
  endpointUrl: string;
  r2BasePath: string;
  logFile: string;
  dryRun: boolean;
  supportedExtensions: string[];
}

// 文件信息接口
interface FileInfo {
  fileName: string;
  filePath: string;
  relativePath: string;
}

// 解析结果接口
interface ParseResult {
  assetFolder: string;
  targetFileName: string;
  r2Key: string;
}

// 上传结果接口
interface UploadResult {
  success: boolean;
  skipped: boolean;
  r2Key?: string;
  error?: string;
}

// 同步结果接口
interface SyncResults {
  total: number;
  success: number;
  skipped: number;
  failed: number;
  uploadedFiles: Array<{ fileName: string; r2Key: string }>;
  failedFiles: Array<{ fileName: string; error: string }>;
}

// 配置
const CONFIG: Config = {
  // 单文件资源目录
  singleFileDir: path.join(__dirname, 'resource/singlefile'),

  // R2 存储桶名称
  bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'uslocal',

  // R2 S3 API 端点 URL
  endpointUrl: `https://${process.env.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,

  // R2 基础路径
  r2BasePath: 'coloring-pages',

  // 日志文件路径
  logFile: path.join(__dirname, 'singlefile-sync.log'),

  // 是否仅模拟执行 (设为 true 进行测试，不会实际修改文件；设为 false 将实际执行修改)
  dryRun: false,

  // 支持的文件类型
  supportedExtensions: ['.png', '.pdf', '.webp', '.jpg', '.jpeg']
};

// 初始化日志
const log = (message: string): void => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(CONFIG.logFile, logMessage + '\n');
};

// 清空日志文件
fs.writeFileSync(CONFIG.logFile, '', 'utf-8');

// 获取文件的 Content-Type
const getContentType = (fileName: string): string => {
  const ext = path.extname(fileName).toLowerCase();

  switch (ext) {
    case '.pdf':
      return 'application/pdf';
    case '.png':
      return 'image/png';
    case '.jpg':
    case '.jpeg':
      return 'image/jpeg';
    case '.webp':
      return 'image/webp';
    default:
      return 'application/octet-stream';
  }
};

// 解析文件名以确定目标路径
const parseFileName = (fileName: string): ParseResult => {
  // 文件名格式示例：
  // assetFolder_monochrome.png -> coloring-pages/assetFolder/assetFolder_monochrome.png
  // assetFolder_colored.webp -> coloring-pages/assetFolder/assetFolder_colored.webp

  const baseName = path.basename(fileName, path.extname(fileName));
  const ext = path.extname(fileName);

  // 尝试从文件名中提取 assetFolder
  let assetFolder = '';
  let targetFileName = fileName;

  // 检查是否包含 _monochrome, _colored 等后缀
  const suffixes = ['_monochrome', '_colored', '_m', '_c'];

  for (const suffix of suffixes) {
    if (baseName.endsWith(suffix)) {
      assetFolder = baseName.substring(0, baseName.length - suffix.length);
      break;
    }
  }

  // 如果没有找到标准后缀，使用整个文件名作为 assetFolder
  if (!assetFolder) {
    assetFolder = baseName;
  }

  return {
    assetFolder,
    targetFileName,
    r2Key: `${CONFIG.r2BasePath}/${assetFolder}/${targetFileName}`
  };
};

// 上传文件到 R2 存储桶
const uploadFileToR2 = (localFilePath: string, fileName: string): UploadResult => {
  try {
    const { assetFolder, targetFileName, r2Key } = parseFileName(fileName);
    const contentType = getContentType(fileName);

    log(`准备上传文件: ${fileName} -> ${r2Key}`);

    // 构建 put-object 命令，使用 --force 覆盖已存在的文件
    const command = `aws s3api put-object \\
      --bucket ${CONFIG.bucketName} \\
      --key "${r2Key}" \\
      --body "${localFilePath}" \\
      --content-type "${contentType}" \\
      --content-disposition "attachment; filename=\\"${targetFileName}\\"" \\
      --endpoint-url ${CONFIG.endpointUrl}`;

    log(`执行命令: ${command}`);

    if (CONFIG.dryRun) {
      log(`[DRY RUN] 跳过执行 put-object 命令: ${r2Key}`);
      return { success: true, skipped: true, r2Key };
    }

    execSync(command, { encoding: 'utf-8' });
    log(`成功上传文件: ${r2Key}`);

    return { success: true, skipped: false, r2Key };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log(`上传文件时出错 (${fileName}): ${errorMessage}`);
    return { success: false, skipped: false, error: errorMessage };
  }
};

// 递归扫描目录获取所有文件
const getAllFiles = (dirPath: string, fileList: FileInfo[] = []): FileInfo[] => {
  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);

    if (stat.isDirectory()) {
      // 递归扫描子目录
      getAllFiles(itemPath, fileList);
    } else if (stat.isFile()) {
      // 检查文件扩展名
      const hasValidExtension = CONFIG.supportedExtensions.includes(path.extname(item).toLowerCase());
      if (hasValidExtension) {
        fileList.push({
          fileName: item,
          filePath: itemPath,
          relativePath: path.relative(CONFIG.singleFileDir, itemPath)
        });
      }
    }
  }

  return fileList;
};

// 扫描并上传单文件目录中的所有文件
const syncSingleFiles = (): { success: boolean; results?: SyncResults; message?: string } => {
  log('开始同步单文件目录...');

  // 检查单文件目录是否存在
  if (!fs.existsSync(CONFIG.singleFileDir)) {
    log(`错误: 单文件目录不存在: ${CONFIG.singleFileDir}`);
    return { success: false, message: '单文件目录不存在' };
  }

  // 递归获取所有支持的文件
  const files = getAllFiles(CONFIG.singleFileDir);

  if (files.length === 0) {
    log('没有找到支持的文件类型');
    return { success: true, message: '没有文件需要同步' };
  }

  log(`找到 ${files.length} 个文件需要同步`);

  let results: SyncResults = {
    total: files.length,
    success: 0,
    skipped: 0,
    failed: 0,
    uploadedFiles: [],
    failedFiles: []
  };

  // 上传每个文件
  for (const fileInfo of files) {
    const { fileName, filePath, relativePath } = fileInfo;
    log(`处理文件: ${relativePath}`);

    const result = uploadFileToR2(filePath, fileName);

    if (result.success) {
      if (result.skipped) {
        results.skipped++;
        log(`[DRY RUN] 跳过文件: ${relativePath}`);
      } else {
        results.success++;
        results.uploadedFiles.push({
          fileName: relativePath,
          r2Key: result.r2Key!
        });
        log(`成功上传文件: ${relativePath} -> ${result.r2Key}`);
      }
    } else {
      results.failed++;
      results.failedFiles.push({
        fileName: relativePath,
        error: result.error!
      });
      log(`上传失败: ${relativePath} - ${result.error}`);
    }
  }

  return {
    success: results.failed === 0,
    results
  };
};

// 主函数
const main = async (): Promise<void> => {
  log('开始单文件同步任务...');
  log(`配置: ${JSON.stringify({
    singleFileDir: CONFIG.singleFileDir,
    bucketName: CONFIG.bucketName,
    r2BasePath: CONFIG.r2BasePath,
    dryRun: CONFIG.dryRun
  }, null, 2)}`);

  const result = syncSingleFiles();

  if (result.success) {
    log('单文件同步完成');
    if (result.results) {
      log(`总结: 总计 ${result.results.total} 个文件, 成功 ${result.results.success}, 跳过 ${result.results.skipped}, 失败 ${result.results.failed}`);

      if (result.results.uploadedFiles.length > 0) {
        log('成功上传的文件:');
        result.results.uploadedFiles.forEach(file => {
          log(`  - ${file.fileName} -> ${file.r2Key}`);
        });
      }

      if (result.results.failedFiles.length > 0) {
        log('上传失败的文件:');
        result.results.failedFiles.forEach(file => {
          log(`  - ${file.fileName}: ${file.error}`);
        });
      }
    }
  } else {
    log(`单文件同步失败: ${result.message || '未知错误'}`);
    process.exit(1);
  }
};

// 执行主函数
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  log(`脚本执行出错: ${errorMessage}`);
  process.exit(1);
});
