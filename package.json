{"name": "printablecoloringhub", "type": "module", "version": "0.0.1", "private": true, "license": "MIT", "scripts": {"dev": "astro dev", "prebuild": "if [ \"$VERCEL\" = \"1\" ]; then cp .yarnrc.vercel .yarnrc 2>/dev/null || true; export SHARP_IGNORE_GLOBAL_LIBVIPS=true; export NODE_OPTIONS=\"--max-old-space-size=4096 --max-http-header-size=16384 --no-warnings\"; export SHARP_DIST_BASE_URL=https://registry.npmjs.org/sharp/-/sharp-0.33.5.tgz; export UV_THREADPOOL_SIZE=64; fi", "build": "astro build", "preview": "astro preview", "astro": "astro", "type-check": "tsc --noEmit", "check-mdx": "node src/scripts/mdx-validator.js", "fix-mdx": "node src/scripts/mdx-validator.js --fix", "format-mdx": "node src/scripts/check-and-fix-mdx.js fix", "test:cloudflare": "node scripts/test-cloudflare-integration.js", "generate-asset-mapping": "node scripts/generate-asset-mapping.js", "rename-assets": "node scripts/rename-asset-files.js", "rename-assets:execute": "node scripts/rename-asset-files.js --execute", "generate-r2-mapping": "node scripts/generate-r2-mapping.js", "upload-to-r2": "node scripts/upload-to-r2.js", "upload-to-r2:execute": "node scripts/upload-to-r2.js --execute", "direct-upload-to-r2": "node scripts/direct-upload-to-r2.js", "direct-upload-to-r2:execute": "node scripts/direct-upload-to-r2.js --execute", "upload-static-to-r2": "node scripts/upload-static-to-r2.js", "upload-static-to-r2:execute": "node scripts/upload-static-to-r2.js --execute", "cleanup-assets": "node scripts/cleanup-local-assets.js", "cleanup-assets:execute": "node scripts/cleanup-local-assets.js --execute"}, "dependencies": {"@astrojs/mdx": "^4.2.6", "@astrojs/sitemap": "^3.4.0", "@astrojs/vercel": "^8.1.4", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.5", "astro": "^5.7.13", "astro-robots-txt": "^1.0.0", "dotenv": "^16.5.0", "flowbite": "^3.1.2", "gray-matter": "^4.0.3", "sharp": "^0.33.5", "tailwindcss": "^4.1.5", "vite": "^5.2.0"}, "devDependencies": {"@aws-sdk/client-s3": "^3.540.0", "@types/fs-extra": "^11.0.4", "@types/mime-types": "^2.1.4", "@types/node": "^20.11.30", "@types/node-fetch": "^2.6.11", "fs-extra": "^11.3.0", "mime-types": "^3.0.1", "node-fetch": "^3.3.2", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.4.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}