# 首页性能优化方案

## 优化概述

针对首页图片过多导致的性能问题，我们实施了以下优化措施：

## 1. 图片数量限制

### 优化前
- 热门页面：显示所有标记为popular的页面（数量不定）
- 新页面：4张
- Hero轮播：5张大图
- 总计：可能超过20-30张图片

### 优化后
- 热门页面：限制为8张 (`popularPages.slice(0, 8)`)
- 新页面：保持4张
- Hero轮播：5张（优化加载策略）
- 总计：最多17张图片

## 2. 懒加载策略

### 智能加载优先级
- **立即加载 (eager)**：前4张关键图片
- **懒加载 (lazy)**：其余所有图片
- **Hero图片**：第一张立即加载，其余懒加载

### 实现方式
```astro
// 新的LazyColoringCard组件
<LazyColoringCard
  priority={index < 4}  // 前4张优先加载
  index={index}         // 用于确定加载策略
/>
```

## 3. Intersection Observer

### 高级懒加载
- **提前加载**：元素进入视口前100px开始加载
- **渐进显示**：图片加载完成后平滑显示
- **错误处理**：加载失败时的优雅降级

### 配置参数
```javascript
{
  threshold: 0.1,        // 10%可见时触发
  rootMargin: '100px',   // 提前100px加载
}
```

## 4. 图片优化

### Cloudflare R2 + CDN
- **自动格式转换**：WebP/AVIF优先
- **响应式尺寸**：根据设备提供合适尺寸
- **质量优化**：卡片图片80%质量，占位符60%

### 占位符策略
- **模糊占位符**：20x20像素低质量版本
- **平滑过渡**：主图加载完成后淡入效果

## 5. 性能配置

### 集中化配置 (`src/config/performance.ts`)
```typescript
export const PERFORMANCE_CONFIG = {
  homepage: {
    maxPopularPages: 8,    // 热门页面限制
    maxNewPages: 4,        // 新页面限制
  },
  lazyLoading: {
    threshold: 0.1,
    rootMargin: '100px',
    eagerLoadCount: 4,     // 立即加载数量
  },
  images: {
    card: { quality: 80 },
    placeholder: { quality: 60 },
  }
};
```

## 6. 用户体验优化

### 下载按钮状态
- **防重复点击**：点击后显示"Downloading..."
- **视觉反馈**：加载动画和状态变化
- **自动恢复**：2秒后恢复原始状态

### 渐进式加载
- **骨架屏效果**：图片加载前的占位符
- **平滑动画**：淡入和位移效果
- **错误处理**：加载失败时的灰度显示

## 7. 性能指标预期改善

### 首屏加载时间 (LCP)
- **优化前**：可能需要加载20+张图片
- **优化后**：只需加载4-5张关键图片

### 累积布局偏移 (CLS)
- **固定尺寸**：所有图片容器预设尺寸
- **占位符**：防止布局跳动

### 首次输入延迟 (FID)
- **延迟加载**：减少主线程阻塞
- **异步解码**：`decoding="async"`

## 8. 浏览器兼容性

### Intersection Observer
- **现代浏览器**：原生支持
- **旧版浏览器**：优雅降级，直接显示所有内容

### 图片格式
- **WebP/AVIF**：现代浏览器自动使用
- **JPEG/PNG**：旧版浏览器回退

## 9. 监控和调试

### 开发工具
- **Network面板**：查看图片加载时序
- **Performance面板**：分析渲染性能
- **Lighthouse**：综合性能评分

### 关键指标
- **图片加载数量**：首屏应≤5张
- **LCP时间**：目标<2.5秒
- **CLS分数**：目标<0.1

## 10. 后续优化建议

### 短期优化
1. **Service Worker**：缓存关键资源
2. **预加载**：关键路径资源预加载
3. **压缩**：进一步优化图片质量

### 长期优化
1. **虚拟滚动**：大列表性能优化
2. **CDN优化**：地理位置就近访问
3. **HTTP/3**：更快的网络传输

## 使用方法

1. **首页已自动应用**：无需额外配置
2. **其他页面**：可使用`LazyColoringCard`组件
3. **自定义配置**：修改`src/config/performance.ts`

这些优化措施将显著提升首页的加载速度和用户体验，特别是在移动设备和慢速网络环境下。
