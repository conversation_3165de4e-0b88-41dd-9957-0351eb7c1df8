---
// Intersection Observer 组件用于实现高级懒加载
interface Props {
  threshold?: number;
  rootMargin?: string;
  class?: string;
}

const {
  threshold = 0.1,
  rootMargin = '50px',
  class: className = '',
} = Astro.props;
---

<div class={`intersection-observer ${className}`} data-threshold={threshold} data-root-margin={rootMargin}>
  <slot />
</div>

<script>
  // 创建 Intersection Observer 来优化图片加载
  function initIntersectionObserver() {
    // 检查浏览器是否支持 Intersection Observer
    if (!('IntersectionObserver' in window)) {
      // 如果不支持，直接显示所有内容
      document.querySelectorAll('.lazy-load-item').forEach(item => {
        item.classList.add('loaded');
      });
      return;
    }

    // 为每个观察器容器创建独立的观察器
    document.querySelectorAll('.intersection-observer').forEach(container => {
      const threshold = parseFloat(container.getAttribute('data-threshold') || '0.1');
      const rootMargin = container.getAttribute('data-root-margin') || '50px';

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const target = entry.target;

            // 添加加载状态
            target.classList.add('loading');

            // 查找目标元素内的懒加载图片
            const lazyImages = target.querySelectorAll('img[loading="lazy"]');
            lazyImages.forEach(img => {
              // 如果图片还没有开始加载
              if (!img.classList.contains('loaded')) {
                img.classList.add('loaded');

                // 添加加载完成监听器
                img.addEventListener('load', () => {
                  img.classList.add('image-loaded');
                  target.classList.add('loaded');
                  target.classList.remove('loading');
                }, { once: true });

                // 添加错误处理
                img.addEventListener('error', () => {
                  img.classList.add('image-error');
                  target.classList.add('loaded');
                  target.classList.remove('loading');
                }, { once: true });
              }
            });

            // 如果没有懒加载图片，直接标记为已加载
            if (lazyImages.length === 0) {
              target.classList.add('loaded');
              target.classList.remove('loading');
            }

            // 停止观察已加载的元素
            observer.unobserve(target);
          }
        });
      }, {
        threshold: threshold,
        rootMargin: rootMargin
      });

      // 观察容器内的所有懒加载项目
      const lazyItems = container.querySelectorAll('.lazy-load-item');
      lazyItems.forEach(item => {
        observer.observe(item);
      });
    });
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initIntersectionObserver);
  } else {
    initIntersectionObserver();
  }

  // 为动态添加的内容提供重新初始化的方法
  (window as any).reinitIntersectionObserver = initIntersectionObserver;
</script>

<style>
  .lazy-load-item {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .lazy-load-item.loading {
    opacity: 0.5;
  }

  .lazy-load-item.loaded {
    opacity: 1;
    transform: translateY(0);
  }

  /* 为不支持 Intersection Observer 的浏览器提供回退 */
  .no-intersection-observer .lazy-load-item {
    opacity: 1;
    transform: translateY(0);
  }

  /* 图片加载状态 */
  .lazy-load-item img {
    transition: opacity 0.3s ease-in-out;
  }

  .lazy-load-item img:not(.image-loaded) {
    opacity: 0.7;
  }

  .lazy-load-item img.image-loaded {
    opacity: 1;
  }

  .lazy-load-item img.image-error {
    opacity: 0.5;
    filter: grayscale(100%);
  }
</style>
