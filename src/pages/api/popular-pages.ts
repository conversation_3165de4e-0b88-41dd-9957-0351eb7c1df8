import type { APIRoute } from 'astro';
import { getPopularColoringPages } from '@/data/coloringPages';
import { PERFORMANCE_CONFIG } from '@/config/performance';

export const GET: APIRoute = async ({ url }) => {
  try {
    // 获取查询参数
    const searchParams = new URLSearchParams(url.search);
    const offset = parseInt(searchParams.get('offset') || '0');
    const limit = parseInt(searchParams.get('limit') || '4'); // 默认每次加载4个（一行）

    // 验证参数
    if (offset < 0 || limit < 1 || limit > 12) {
      return new Response(
        JSON.stringify({ error: 'Invalid parameters' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // 获取数据
    const pages = await getPopularColoringPages(limit, offset);

    // 返回JSON响应
    return new Response(
      JSON.stringify({
        pages,
        offset,
        limit,
        hasMore: pages.length === limit,
        loadedCount: pages.length,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=300', // 缓存5分钟
        },
      }
    );
  } catch (error) {
    console.error('Error in popular-pages API:', error);

    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
