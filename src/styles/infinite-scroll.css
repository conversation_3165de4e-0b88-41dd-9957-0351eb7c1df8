/* 无限滚动和瀑布流样式 */

/* 初始卡片动画 - 页面加载时 */
.coloring-card {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.coloring-card.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* 初始加载时错开动画时间 */
.coloring-card:nth-child(1) { animation-delay: 0.1s; }
.coloring-card:nth-child(2) { animation-delay: 0.2s; }
.coloring-card:nth-child(3) { animation-delay: 0.3s; }
.coloring-card:nth-child(4) { animation-delay: 0.4s; }

/* 懒加载显示卡片样式 */
.lazy-reveal-card {
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.lazy-reveal-card.hidden {
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
}

.lazy-reveal-card.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* 行级显示动画 */
.lazy-reveal-card.revealing {
  animation: revealRow 0.6s ease-out forwards;
}

@keyframes revealRow {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 加载指示器动画 */
.loading-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式网格优化 */
@media (min-width: 640px) {
  #coloring-pages-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  #coloring-pages-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  #coloring-pages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 瀑布流效果（可选） */
@supports (display: grid) and (grid-template-rows: masonry) {
  #coloring-pages-grid {
    grid-template-rows: masonry;
    align-items: start;
  }
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 性能优化 */
#coloring-pages-grid {
  /* 启用GPU加速 */
  transform: translateZ(0);
  /* 优化重绘性能 */
  contain: layout style paint;
}

/* 卡片性能优化 */
.coloring-card {
  /* 启用GPU加速 */
  transform: translateZ(0);
  /* 优化合成层 */
  will-change: transform, opacity;
  /* 减少重绘 */
  contain: layout style paint;
}

/* 动画完成后移除will-change */
.coloring-card.loaded:not(.newly-loaded) {
  will-change: auto;
}

/* 加载指示器优化 */
#loading-indicator {
  /* 避免布局抖动 */
  min-height: 60px;
  /* GPU加速 */
  transform: translateZ(0);
}

/* 加载状态的卡片骨架屏 */
.card-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 错误状态样式 */
.error-message {
  color: #ef4444;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

/* 成功加载动画 */
.success-indicator {
  color: #10b981;
  animation: checkmark 0.6s ease-in-out;
}

@keyframes checkmark {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 优化移动端触摸体验 */
@media (max-width: 768px) {
  .coloring-card {
    transition: transform 0.2s ease-out;
  }

  .coloring-card:active {
    transform: scale(0.98);
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .coloring-card,
  .loading-indicator,
  html {
    animation: none;
    transition: none;
  }

  html {
    scroll-behavior: auto;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .card-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  }

  .error-message {
    background-color: #1f2937;
    border-color: #374151;
    color: #f87171;
  }
}
